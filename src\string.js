const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * Generate random noise characters (using non-base64 characters for obfuscation)
 * @param {number} count - Number of noise characters to generate
 * @returns {string} - Random noise characters
 */
function generateNoise(count) {
    const noiseChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`\\/|\"\'';
    let noise = '';
    for (let i = 0; i < count; i++) {
        noise += noiseChars[Math.floor(Math.random() * noiseChars.length)];
    }
    return noise;
}

/**
 * Encode function with noise and no padding
 * @param {string} str - String to encode
 * @returns {string} - Encoded string with noise
 */
function encodeToBase64(str) {
    try {
        if (typeof str !== 'string') {
            console.warn('encodeToBase64: Input is not a string:', typeof str, str);
            return '';
        }
        if (str.length > 100000) {
            console.warn('encodeToBase64: String too long, skipping:', str.length);
            return str;
        }

        // Standard base64 encoding
        let base64 = Buffer.from(str, 'utf8').toString('base64');

        // Remove padding
        base64 = base64.replace(/=/g, '');

        // Add noise characters at regular intervals
        let noisyStr = '';
        for (let i = 0; i < base64.length; i++) {
            noisyStr += base64[i];
            // Add noise every 2 or 3 characters
            if ((i + 1) % 2 === 0 || (i + 1) % 3 === 0) {
                noisyStr += generateNoise(1);
            }
        }

        return noisyStr;
    } catch (error) {
        console.error('Error encoding string to Base64:', error, 'String:', str);
        return str;
    }
}

/**
 * Generate decoy strings that look like real encoded strings
 * @param {number} count - Number of decoy strings to generate
 * @returns {Array<string>} - Array of decoy strings
 */
function generateDecoyStrings(count) {
    const decoys = [];
    const sampleWords = ['hello', 'world', 'test', 'sample', 'data', 'string', 'value', 'content'];

    for (let i = 0; i < count; i++) {
        const randomWord = sampleWords[Math.floor(Math.random() * sampleWords.length)];
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        const fakeString = randomWord + randomSuffix;
        decoys.push(encodeToBase64(fakeString));
    }

    return decoys;
}

/**
 * Process string literals in JavaScript code using Babel AST
 * @param {string} code - JavaScript code to process
 * @returns {Object} - Object containing transformed code and statistics
 */
function processStringLiteralsWithArray(code) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        const base64StringArray = [];
        const realStringMap = new Map(); // Track real strings and their final indices
        let transformCount = 0;

        // First pass: collect all real strings and store their encoded versions
        const originalToEncodedMap = new Map(); // Map original strings to their encoded versions
        const realStrings = [];

        traverse(ast, {
            StringLiteral(path) {
                const originalString = path.node.value;
                if (originalString.length >= 2) {
                    if (!originalToEncodedMap.has(originalString)) {
                        const base64String = encodeToBase64(originalString);
                        if (base64String && base64String !== originalString) {
                            originalToEncodedMap.set(originalString, base64String);
                            realStrings.push(base64String);
                        }
                    }
                }
            },
            TemplateLiteral(path) {
                if (path.node.expressions.length === 0 && path.node.quasis.length === 1) {
                    const quasi = path.node.quasis[0];
                    if (quasi.value.raw.length >= 2) {
                        const originalString = quasi.value.raw;
                        if (!originalToEncodedMap.has(originalString)) {
                            const base64String = encodeToBase64(originalString);
                            if (base64String && base64String !== originalString) {
                                originalToEncodedMap.set(originalString, base64String);
                                realStrings.push(base64String);
                            }
                        }
                    }
                }
            }
        });

        // Create the final array with decoys and real strings
        const decoyCount = Math.max(3, Math.floor(Math.random() * 8) + 2);
        const decoyStrings = generateDecoyStrings(decoyCount);

        // Mix real strings and decoys
        const allStrings = [...realStrings, ...decoyStrings];

        // Shuffle the array to mix decoys with real strings
        for (let i = allStrings.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [allStrings[i], allStrings[j]] = [allStrings[j], allStrings[i]];
        }

        base64StringArray.push(...allStrings);

        // Create mapping of real strings to their final indices
        realStrings.forEach(realString => {
            const finalIndex = base64StringArray.indexOf(realString);
            realStringMap.set(realString, finalIndex);
        });

        // Second pass: replace strings with correct indices using stored encoded versions
        traverse(ast, {
            StringLiteral(path) {
                const originalString = path.node.value;

                if (originalString.length < 2) return;

                const base64String = originalToEncodedMap.get(originalString);
                if (!base64String) return;

                const arrayIndex = realStringMap.get(base64String);
                if (arrayIndex === undefined) return;

                // Check if this string literal is being used as an object property key
                const parent = path.parent;
                if (t.isObjectProperty(parent) && parent.key === path.node && !parent.computed) {
                    // Skip transformation for object property keys that are not computed
                    // This prevents the error where CallExpression cannot be used as property key
                    return;
                }

                const decodeCall = t.memberExpression(
                    t.identifier('base64Strings'),
                    t.numericLiteral(arrayIndex),
                    true // Computed property
                );

                path.replaceWith(
                    t.callExpression(t.identifier('customDecode'), [decodeCall])
                );

                transformCount++;
            },

            TemplateLiteral(path) {
                if (path.node.expressions.length === 0 && path.node.quasis.length === 1) {
                    const quasi = path.node.quasis[0];
                    if (quasi.value.raw.length < 2) return;

                    const originalString = quasi.value.raw;
                    const base64String = originalToEncodedMap.get(originalString);
                    if (!base64String) return;

                    const arrayIndex = realStringMap.get(base64String);
                    if (arrayIndex === undefined) return;

                    // Check if this template literal is being used as an object property key
                    const parent = path.parent;
                    if (t.isObjectProperty(parent) && parent.key === path.node && !parent.computed) {
                        // Skip transformation for object property keys that are not computed
                        return;
                    }

                    const decodeCall = t.memberExpression(
                        t.identifier('base64Strings'),
                        t.numericLiteral(arrayIndex),
                        true
                    );

                    path.replaceWith(
                        t.callExpression(t.identifier('customDecode'), [decodeCall])
                    );

                    transformCount++;
                }
            }
        });

        // Create the custom decoder function
        const customDecoderFunction = t.functionDeclaration(
            t.identifier('customDecode'),
            [t.identifier('encodedStr')],
            t.blockStatement([
                // Remove noise and restore padding
                t.variableDeclaration('const', [
                    t.variableDeclarator(
                        t.identifier('base64Chars'),
                        t.stringLiteral('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/')
                    )
                ]),
                t.variableDeclaration('let', [
                    t.variableDeclarator(
                        t.identifier('cleanStr'),
                        t.stringLiteral('')
                    )
                ]),
                t.forStatement(
                    t.variableDeclaration('let', [t.variableDeclarator(t.identifier('i'), t.numericLiteral(0))]),
                    t.binaryExpression('<', t.identifier('i'), t.memberExpression(t.identifier('encodedStr'), t.identifier('length'))),
                    t.updateExpression('++', t.identifier('i')),
                    t.blockStatement([
                        t.variableDeclaration('const', [
                            t.variableDeclarator(
                                t.identifier('char'),
                                t.memberExpression(t.identifier('encodedStr'), t.identifier('i'), true)
                            )
                        ]),
                        t.ifStatement(
                            t.callExpression(
                                t.memberExpression(t.identifier('base64Chars'), t.identifier('includes')),
                                [t.identifier('char')]
                            ),
                            t.expressionStatement(
                                t.assignmentExpression('+=', t.identifier('cleanStr'), t.identifier('char'))
                            )
                        )
                    ])
                ),
                // Restore padding
                t.whileStatement(
                    t.binaryExpression('!==', t.binaryExpression('%', t.memberExpression(t.identifier('cleanStr'), t.identifier('length')), t.numericLiteral(4)), t.numericLiteral(0)),
                    t.expressionStatement(
                        t.assignmentExpression('+=', t.identifier('cleanStr'), t.stringLiteral('='))
                    )
                ),
                // Custom base64 decode logic
                t.variableDeclaration('let', [
                    t.variableDeclarator(t.identifier('result'), t.stringLiteral('')),
                    t.variableDeclarator(t.identifier('buffer'), t.numericLiteral(0)),
                    t.variableDeclarator(t.identifier('bitsCollected'), t.numericLiteral(0))
                ]),
                t.forStatement(
                    t.variableDeclaration('let', [t.variableDeclarator(t.identifier('i'), t.numericLiteral(0))]),
                    t.binaryExpression('<', t.identifier('i'), t.memberExpression(t.identifier('cleanStr'), t.identifier('length'))),
                    t.updateExpression('++', t.identifier('i')),
                    t.blockStatement([
                        t.variableDeclaration('const', [
                            t.variableDeclarator(
                                t.identifier('char'),
                                t.memberExpression(t.identifier('cleanStr'), t.identifier('i'), true)
                            )
                        ]),
                        t.variableDeclaration('const', [
                            t.variableDeclarator(
                                t.identifier('charIndex'),
                                t.callExpression(
                                    t.memberExpression(t.identifier('base64Chars'), t.identifier('indexOf')),
                                    [t.identifier('char')]
                                )
                            )
                        ]),
                        t.ifStatement(
                            t.binaryExpression('===', t.identifier('charIndex'), t.unaryExpression('-', t.numericLiteral(1))),
                            t.continueStatement()
                        ),
                        t.expressionStatement(
                            t.assignmentExpression('=', t.identifier('buffer'),
                                t.binaryExpression('|',
                                    t.binaryExpression('<<', t.identifier('buffer'), t.numericLiteral(6)),
                                    t.identifier('charIndex')
                                )
                            )
                        ),
                        t.expressionStatement(
                            t.assignmentExpression('+=', t.identifier('bitsCollected'), t.numericLiteral(6))
                        ),
                        t.ifStatement(
                            t.binaryExpression('>=', t.identifier('bitsCollected'), t.numericLiteral(8)),
                            t.blockStatement([
                                t.expressionStatement(
                                    t.assignmentExpression('-=', t.identifier('bitsCollected'), t.numericLiteral(8))
                                ),
                                t.expressionStatement(
                                    t.assignmentExpression('+=', t.identifier('result'),
                                        t.callExpression(
                                            t.memberExpression(t.identifier('String'), t.identifier('fromCharCode')),
                                            [t.binaryExpression('&',
                                                t.binaryExpression('>>', t.identifier('buffer'), t.identifier('bitsCollected')),
                                                t.numericLiteral(0xFF)
                                            )]
                                        )
                                    )
                                )
                            ])
                        )
                    ])
                ),
                t.returnStatement(t.identifier('result'))
            ])
        );

        const arrayDeclaration = t.variableDeclaration('const', [
            t.variableDeclarator(
                t.identifier('base64Strings'),
                t.arrayExpression(base64StringArray.map(str => t.stringLiteral(str)))
            )
        ]);

        ast.program.body.unshift(customDecoderFunction);
        ast.program.body.unshift(arrayDeclaration);

        return {
            code: generate(ast).code,
            transformCount,
            base64StringArray
        };
    } catch (error) {
        console.error('Error processing string literals:', error);
        return {
            code,
            transformCount: 0,
            base64StringArray: []
        };
    }
}

module.exports = {
    processStringEncoding(code) {
        return processStringLiteralsWithArray(code);
    }
};
