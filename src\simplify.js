const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * Identifiers that should never be simplified
 */
const RESERVED_IDENTIFIERS = new Set([
    'undefined', 'null', 'true', 'false', 'NaN', 'Infinity',
    'arguments', 'this', 'super'
]);

/**
 * Detects global identifiers in the code using Babel scope analysis
 * @param {string} code - JavaScript code to analyze
 * @returns {Set} - Set of global identifiers found in the code
 */
function detectGlobalIdentifiers(code) {
    const foundGlobals = new Set();

    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        // First pass: collect all declared identifiers in the entire program
        const allDeclaredNames = new Set();

        traverse(ast, {
            // Variable declarations
            VariableDeclarator(path) {
                if (t.isIdentifier(path.node.id)) {
                    allDeclaredNames.add(path.node.id.name);
                }
            },

            // Function declarations
            FunctionDeclaration(path) {
                if (path.node.id) {
                    allDeclaredNames.add(path.node.id.name);
                }
            },

            // Class declarations
            ClassDeclaration(path) {
                if (path.node.id) {
                    allDeclaredNames.add(path.node.id.name);
                }
            },

            // Function parameters
            Function(path) {
                path.node.params.forEach(param => {
                    if (t.isIdentifier(param)) {
                        allDeclaredNames.add(param.name);
                    }
                });
            }
        });

        // Second pass: find actual global references
        traverse(ast, {
            Identifier(path) {
                const name = path.node.name;

                // Skip reserved identifiers that shouldn't be simplified
                if (RESERVED_IDENTIFIERS.has(name)) return;

                // Skip if it's a property name in member expression (non-computed)
                if (t.isMemberExpression(path.parent) && path.parent.property === path.node && !path.parent.computed) {
                    return;
                }

                // Skip if it's a property name in object expression
                if (t.isObjectProperty(path.parent) && path.parent.key === path.node && !path.parent.computed) {
                    return;
                }

                // Skip if it's a method name in object method
                if (t.isObjectMethod(path.parent) && path.parent.key === path.node) {
                    return;
                }

                // Skip if it's a function/variable/class declaration name
                if (t.isFunctionDeclaration(path.parent) && path.parent.id === path.node) {
                    return;
                }

                if (t.isVariableDeclarator(path.parent) && path.parent.id === path.node) {
                    return;
                }

                if (t.isClassDeclaration(path.parent) && path.parent.id === path.node) {
                    return;
                }

                // Skip if it's a parameter name
                if (t.isFunctionExpression(path.parent) || t.isArrowFunctionExpression(path.parent)) {
                    if (path.parent.params && path.parent.params.includes(path.node)) {
                        return;
                    }
                }

                // Skip if it's a label
                if (t.isLabeledStatement(path.parent) && path.parent.label === path.node) {
                    return;
                }

                // skip this
                if (t.isThisExpression(path.parent) && path.parent === path.node) {
                    return;
                }

                // Check if it's actually a global reference
                // 1. Not declared anywhere in the program
                // 2. No local binding in current scope
                if (!allDeclaredNames.has(name)) {
                    const binding = path.scope.getBinding(name);
                    if (!binding) {
                        foundGlobals.add(name);
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error parsing code for global detection:', error);
    }

    return foundGlobals;
}

/**
 * Transforms JavaScript code by converting dot notation to bracket notation for ALL objects
 * @param {string} code - JavaScript code to transform
 * @returns {string} - Transformed code
 */
function transformDotNotation(code) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        let transformCount = 0;

        traverse(ast, {
            MemberExpression(path) {
                // Only transform non-computed member expressions (dot notation)
                if (path.node.computed) return;

                // Transform ALL member expressions, not just globals
                // Convert dot notation to bracket notation for everything
                // e.g., console.log -> console['log']
                // e.g., _0x331ca3.length -> _0x331ca3['length']
                // e.g., navigator.clipboard.writeText -> navigator['clipboard']['writeText']
                if (t.isIdentifier(path.node.property)) {
                    const propertyName = path.node.property.name;
                    path.node.computed = true;
                    path.node.property = t.stringLiteral(propertyName);
                    transformCount++;
                }
            }
        });

        return {
            code: generate(ast).code,
            transformCount
        };
    } catch (error) {
        console.error('Error transforming dot notation:', error);
        return {
            code,
            transformCount: 0
        };
    }
}

/**
 * Main function to process JavaScript code with dot notation simplification
 * @param {string} code - JavaScript code to process
 * @returns {Object} - Object containing transformed code and statistics
 */
function processSimplify(code) {
    try {
        // Step 1: Detect global identifiers in the code
        const foundGlobals = detectGlobalIdentifiers(code);

        if (foundGlobals.size === 0) {
            return {
                code,
                simplifiedCount: 0,
                foundGlobals: []
            };
        }

        // Step 2: Transform dot notation to bracket notation for ALL member expressions
        const transformResult = transformDotNotation(code);

        return {
            code: transformResult.code,
            simplifiedCount: transformResult.transformCount,
            foundGlobals: Array.from(foundGlobals)
        };
    } catch (error) {
        console.error('Error processing simplification:', error);
        return {
            code,
            simplifiedCount: 0,
            foundGlobals: []
        };
    }
}

module.exports = {
    processSimplify
};